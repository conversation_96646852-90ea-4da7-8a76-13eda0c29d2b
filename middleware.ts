import { NextRequest, NextResponse } from 'next/server';
import { locales, defaultLocale } from './i18n.config';

// 公开可访问的路由（未登录用户可以访问）
const PUBLIC_ROUTES = [
  '/', // 首页
  '/login', // 登录页
  '/contact', // 联系我们
  '/guide', // 引导页
  '/terms-of-service', // 服务条款
  '/privacy-policy', // 隐私政策
  '/pricing', // 费用
  '/invite', // 邀请
  '/forgot-password', // 忘记密码
];

// 认证相关的路由（已登录用户不应该访问）
const AUTH_ROUTES = ['/login'];

// 始终需要认证的路由
const PROTECTED_ROUTES = ['/profile', '/exam', '/history', '/notes', '/invite', '/pricing', '/space', '/demo'];

// 语言检测和处理
function detectAndProcessLocale(request: NextRequest): {
  locale: string;
  pathname: string;
  routePath: string;
  hasLocale: boolean;
  shouldRewrite: boolean;
} {
  const pathname = request.nextUrl.pathname;

  // 检查URL中是否已经包含locale
  const pathnameHasLocale = locales.some((locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`);

  if (pathnameHasLocale) {
    const locale = pathname.split('/')[1];
    const routePath = pathname.replace(`/${locale}`, '') || '/';
    return { locale, pathname, routePath, hasLocale: true, shouldRewrite: false };
  }

  // 获取用户首选语言
  const locale = getUserPreferredLocale(request);
  
  // 如果是默认语言（en）且是根路径或根路径下的路由，不需要重定向，只需要重写
  if (locale === defaultLocale) {
    return {
      locale,
      pathname,
      routePath: pathname,
      hasLocale: false,
      shouldRewrite: true // 标记需要重写而不是重定向
    };
  }
  
  return {
    locale,
    pathname,
    routePath: pathname,
    hasLocale: false,
    shouldRewrite: false
  };
}

// 获取用户首选语言（优化版）
function getUserPreferredLocale(request: NextRequest): string {
  // 0. 从 headers 获取 IP 地理位置
  const ipCountry = request.headers.get('x-vercel-ip-country') || request.headers.get('CF-IPCountry');
  const country = ipCountry?.toUpperCase();

  // 1. 优先检查请求头中的语言偏好（客户端设置）
  const preferredLocale = request.headers.get('x-preferred-locale');
  if (preferredLocale && locales.includes(preferredLocale as any)) {
    return preferredLocale;
  }

  // 2. 检查cookie中的语言偏好
  const cookieLocale = request.cookies.get('preferred_locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    return cookieLocale;
  }

  // 3. 从Accept-Language头获取首选语言，并结合地理位置
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const detectedLocale = parseAcceptLanguage(acceptLanguage, country);
    if (detectedLocale) {
      return detectedLocale;
    }
  }

  // 4. 根据地理位置进行回退
  if (country) {
    if (['HK', 'TW', 'MO'].includes(country)) return 'zh-HK';
    if (['CN', 'SG'].includes(country)) return 'zh';
  }

  return defaultLocale;
}

// 解析Accept-Language头（优化版）
function parseAcceptLanguage(acceptLanguage: string, country?: string): string | null {
  const languages = acceptLanguage
    .split(',')
    .map((lang) => {
      const [code, quality = '1'] = lang.trim().split(';q=');
      return { code: code.trim(), quality: parseFloat(quality) };
    })
    .sort((a, b) => b.quality - a.quality);

  for (const { code } of languages) {
    // 精确匹配
    if (locales.includes(code as any)) {
      return code;
    }

    // 处理变体语言
    if (code.startsWith('zh-')) {
      if (code.includes('HK') || code.includes('TW') || code.includes('Hant')) {
        return 'zh-HK';
      }
      return 'zh';
    }

    // 处理基础语言代码
    const baseCode = code.split('-')[0];
    if (baseCode === 'zh') {
      // 结合国家代码判断
      if (country && ['HK', 'TW', 'MO'].includes(country)) {
        return 'zh-HK';
      }
      return 'zh';
    }
    if (baseCode === 'en') return 'en';
  }

  return null;
}

// 检查用户认证状态（优化版 - 只检查登录状态，不处理token）
function checkAuthenticationStatus(request: NextRequest): {
  isAuthenticated: boolean;
  authSource: 'cookie' | 'header' | 'none';
} {
  // 检查cookie中的登录状态（不再检查token）
  const cookieAuth = request.cookies.get('isAuthenticated')?.value;
  if (cookieAuth === 'true') {
    return {
      isAuthenticated: true,
      authSource: 'cookie'
    };
  }

  // 检查Authorization header（用于API调用）
  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return {
      isAuthenticated: true,
      authSource: 'header'
    };
  }

  return { isAuthenticated: false, authSource: 'none' };
}

// 路由权限检查
function checkRoutePermissions(
  routePath: string,
  isAuthenticated: boolean
): {
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
} {
  // 已登录用户访问认证路由（如登录页）
  if (isAuthenticated && AUTH_ROUTES.some((route) => routePath.startsWith(route))) {
    return {
      shouldRedirect: true,
      redirectTo: '/',
      reason: 'authenticated_user_accessing_auth_route'
    };
  }

  // 未登录用户访问受保护路由
  if (!isAuthenticated) {
    const isPublic = PUBLIC_ROUTES.some((route) => {
      if (route === '/') return routePath === '/';
      return routePath.startsWith(route);
    });

    const isProtected = PROTECTED_ROUTES.some((route) => routePath.startsWith(route));

    if (!isPublic && isProtected) {
      return {
        shouldRedirect: true,
        redirectTo: '/login',
        reason: 'unauthenticated_user_accessing_protected_route'
      };
    }
  }

  return { shouldRedirect: false };
}

// 创建重定向响应
function createRedirectResponse(
  request: NextRequest,
  redirectPath: string,
  locale: string,
  originalPath?: string
): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${redirectPath}`;

  // 如果是重定向到登录页，保存原始路径
  if (redirectPath === '/login' && originalPath && originalPath !== '/login') {
    url.searchParams.set('redirect', originalPath);
  }

  return NextResponse.redirect(url);
}

// 创建重写响应（用于默认语言）
function createRewriteResponse(
  request: NextRequest,
  locale: string
): NextResponse {
  const url = request.nextUrl.clone();
  // 重写到 /en/xxx 但不改变用户看到的URL
  url.pathname = `/${locale}${url.pathname}`;
  return NextResponse.rewrite(url);
}

// 创建响应头增强的Next响应
function createEnhancedResponse(locale: string, isAuthenticated: boolean, authSource: string): NextResponse {
  const response = NextResponse.next();

  // 设置响应头供客户端使用
  response.headers.set('x-user-authenticated', isAuthenticated ? 'true' : 'false');
  response.headers.set('x-current-locale', locale);
  response.headers.set('x-auth-source', authSource);

  // 设置语言偏好cookie（如果没有的话）
  if (!response.cookies.get('preferred_locale')) {
    response.cookies.set('preferred_locale', locale, {
      path: '/',
      maxAge: 365 * 24 * 60 * 60, // 1年
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    });
  }

  return response;
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 跳过静态文件和API路由
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon') ||
    pathname.startsWith('/robots.txt') ||
    pathname.startsWith('/sitemap')
  ) {
    return NextResponse.next();
  }

  // 检测和处理语言
  const { locale, routePath, hasLocale, shouldRewrite } = detectAndProcessLocale(request);

  // 检查认证状态
  const { isAuthenticated, authSource } = checkAuthenticationStatus(request);

  // 开发环境调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Middleware] ${new Date().toISOString()}`);
    console.log(`  Path: ${pathname} -> Route: ${routePath}`);
    console.log(`  Locale: ${locale} (hasLocale: ${hasLocale}, shouldRewrite: ${shouldRewrite})`);
    console.log(`  Auth: ${isAuthenticated} (source: ${authSource})`);
  }

  // 检查路由权限
  const { shouldRedirect, redirectTo, reason } = checkRoutePermissions(routePath, isAuthenticated);

  if (shouldRedirect && redirectTo) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`  Redirecting: ${reason} -> ${redirectTo}`);
    }
    return createRedirectResponse(request, redirectTo, locale, pathname);
  }

  // 如果需要重写（默认语言不显示前缀）
  if (shouldRewrite) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`  Rewriting for default locale: ${locale}`);
    }
    const response = createRewriteResponse(request, locale);
    // 设置响应头
    response.headers.set('x-user-authenticated', isAuthenticated ? 'true' : 'false');
    response.headers.set('x-current-locale', locale);
    response.headers.set('x-auth-source', authSource);
    
    // 设置语言偏好cookie
    if (!response.cookies.get('preferred_locale')) {
      response.cookies.set('preferred_locale', locale, {
        path: '/',
        maxAge: 365 * 24 * 60 * 60, // 1年
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production'
      });
    }
    return response;
  }

  // 如果需要添加locale（非默认语言），进行重定向
  if (!hasLocale) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`  Adding locale: ${locale}`);
    }
    const url = request.nextUrl.clone();
    url.pathname = `/${locale}${pathname}`;
    return NextResponse.redirect(url);
  }

  // 返回增强的响应
  return createEnhancedResponse(locale, isAuthenticated, authSource);
}

export const config = {
  matcher: [
    // 匹配所有路径，除了内部Next.js路径和静态文件
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|.*\\..*).*)'
  ]
};
