# ChatComponent 自动发送逻辑重构

## 📋 重构概述

重构了 ChatComponent 组件的自动发送方法，统一处理所有内容类型（video、audio、doc），并根据类型使用不同的 endpoint。

## 🎯 主要改进

### 1. 统一的类型支持

**之前：** 分别处理 video 和 audio 类型
```typescript
// 旧的分离逻辑
const isVideoDescribe = contentData?.type === 'video' && !contentData?.fileUrl && isAutoSend;
const isAudioDescribe = contentData?.type === 'audio' && !contentData?.fileUrl && isAutoSend;

let endpoint: string | undefined;
if (isVideoDescribe) {
  endpoint = '/llm/video/describe';
} else if (isAudioDescribe) {
  endpoint = '/llm/audio/describe';
}
```

**现在：** 统一处理所有类型
```typescript
// 新的统一逻辑
const getEndpointForType = useCallback((type?: 'video' | 'audio' | 'doc'): string | undefined => {
  switch (type) {
    case 'video':
      return '/llm/video/describe';
    case 'audio':
      return '/llm/audio/describe';
    case 'doc':
      return '/llm/doc/describe';
    default:
      return undefined;
  }
}, []);
```

### 2. 新增类型字段支持

**NewArticleChat 接口更新：**
```typescript
interface NewArticleChat {
  modelId: string
  aiQuery: string
  type?: 'video' | 'audio' | 'doc' // 新增类型字段
}
```

**ChatComponent Props 更新：**
```typescript
interface ChatComponentProps {
  contentId: number;
  initialInputValue?: string;
  initialSelectedModelId?: string;
  autoSend?: boolean;
  autoSendType?: 'video' | 'audio' | 'doc'; // 新增：自动发送类型
  onAutoSendComplete?: () => void;
  contentData?: any;
}
```

### 3. 智能 Endpoint 选择

```typescript
// 统一处理所有类型的 endpoint
let endpoint: string | undefined;
if (isAutoSend) {
  // 优先使用传入的 autoSendType，然后使用 contentData 中的类型
  const typeToUse = autoSendType || contentData?.type;
  const shouldUseSpecialEndpoint = typeToUse && !contentData?.fileUrl;
  
  if (shouldUseSpecialEndpoint) {
    endpoint = getEndpointForType(typeToUse);
    console.log(`🎯 Using special endpoint for ${typeToUse}: ${endpoint}`);
  }
}
```

### 4. 自动刷新 ContentData

**新增功能：** video 和 audio 类型完成后自动刷新数据
```typescript
() => {
  // 确保加载状态被正确清除
  setIsLoading(false);
  
  // 如果是 video 或 audio 类型的自动发送，完成后刷新 contentData
  if (isAutoSend && endpoint && (autoSendType === 'video' || autoSendType === 'audio')) {
    console.log(`🔄 Refreshing contentData for ${autoSendType} type after auto-send completion`);
    queryClient.invalidateQueries({ 
      queryKey: queryKeys.contentDetail(contentId) 
    });
  }
}
```

## 🔄 数据流程

### 1. 页面初始化
```typescript
// app/[locale]/article/[id]/page.tsx
const shouldAutoSend = (
  (contentResponse.data.type === 'video' && !contentResponse.data.fileUrl) ||
  (contentResponse.data.type === 'audio' && !contentResponse.data.fileUrl) ||
  (contentResponse.data.type === 'doc' && !contentResponse.data.fileUrl)
);

if (shouldAutoSend) {
  const contentType = contentResponse.data.type as 'video' | 'audio' | 'doc';
  setNewArticleChatData({
    initialInputValue: '',
    initialSelectedModelId: '',
    autoSend: true,
    type: contentType // 传递类型信息
  });
}
```

### 2. 组件传递
```typescript
// components/article/right-sidebar.tsx
<ChatComponent
  contentId={contentId || 0}
  initialInputValue={newArticleChatData?.initialInputValue}
  initialSelectedModelId={newArticleChatData?.initialSelectedModelId}
  autoSend={newArticleChatData?.autoSend || false}
  autoSendType={newArticleChatData?.type} // 传递类型
  onAutoSendComplete={onAutoSendComplete}
  contentData={contentData}
/>
```

### 3. 自动发送执行
```typescript
// components/article/chat-component.tsx
const handleSendMessage = useCallback(async (messageText?: string, model?: Model) => {
  // 根据类型选择正确的 endpoint
  const typeToUse = autoSendType || contentData?.type;
  const endpoint = getEndpointForType(typeToUse);
  
  // 发送请求...
  
  // 完成后刷新数据（仅 video/audio）
}, [/* 依赖项 */]);
```

## 📊 支持的 Endpoint 映射

| 内容类型 | Endpoint | 说明 |
|---------|----------|------|
| `video` | `/llm/video/describe` | 视频描述生成 |
| `audio` | `/llm/audio/describe` | 音频描述生成 |
| `doc` | `/llm/doc/describe` | 文档描述生成 |
| 其他 | 默认 `/llm/chat` | 标准聊天接口 |

## 🔧 配置说明

### 自动刷新配置
- **video 类型：** 完成后自动刷新 contentData
- **audio 类型：** 完成后自动刷新 contentData  
- **doc 类型：** 不自动刷新（根据需求可调整）

### 触发条件
自动发送触发条件：
1. `contentData.type` 为 `video`、`audio` 或 `doc`
2. `contentData.fileUrl` 为空或不存在
3. `autoSend` 为 `true`

## 🚀 使用示例

### 手动触发自动发送
```typescript
setNewArticleChatData({
  initialInputValue: '请分析这个视频内容',
  initialSelectedModelId: 'gpt-4',
  autoSend: true,
  type: 'video'
});
```

### 从 NewArticleChat 数据触发
```typescript
// 在 article-cache-store 中设置
setNewArticleChat(contentId, {
  modelId: 'gpt-4',
  aiQuery: '请分析这个音频内容',
  type: 'audio'
});
```

## ✅ 测试要点

1. **类型识别：** 确保不同类型使用正确的 endpoint
2. **自动刷新：** video/audio 完成后 contentData 是否更新
3. **错误处理：** 各种错误情况的处理
4. **状态管理：** 自动发送完成后状态清理
5. **跨组件传递：** 类型信息在组件间正确传递

## 🔮 未来扩展

1. **新增内容类型：** 可轻松添加新的内容类型和对应 endpoint
2. **自定义 endpoint：** 支持动态配置 endpoint
3. **批量处理：** 支持多个内容的批量自动发送
4. **进度跟踪：** 添加自动发送进度显示
