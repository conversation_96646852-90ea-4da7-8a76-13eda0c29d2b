'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Grid3X3,
  List,
  ImageIcon,
  Video,
  FileText,
  Users,
  FileTextIcon as FileText2,
  Eye,
  Calendar,
  Edit3,
  Check,
  X
} from 'lucide-react';
import { CreateExamModal } from '@/components/space/create-exam-modal';
import { ContentGrid } from '@/components/ui/content-grid';
import { LearningHub } from '@/components/learning-hub';
import { useTranslation } from '@/lib/translation-context';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { useAppStore } from '@/lib/stores';
import type { Space } from '@/lib/stores/app-data-store';
import { getContentPageContent } from '@/servers/api/wendangneirong';
import { useRouter } from 'next/navigation';
import { SpaceChatWindow } from '@/components/space/space-chat-window';
import { toast } from 'sonner';

interface SpacePost {
  id: string;
  author: {
    name: string;
    avatar: string;
    verified?: boolean;
  };
  content: string;
  type: 'text' | 'image' | 'video' | 'document';
  mediaUrl?: string;
  timestamp: string;
  likes: number;
  comments: number;
  tags: string[];
}

// 内容列表项类型
interface ContentItem {
  id?: number;
  createBy?: number;
  updateBy?: number;
  createAt?: string;
  updateAt?: string;
  spaceId?: number;
  memberId?: number;
  uploadId?: number;
  fileUrl?: string;
  title?: string;
}

export default function SpacePage() {
  const params = useParams();
  const spaceId = params.id as string;
  const { getSpaceById, updateSpace } = useAppDataStore();
  const { toggleChat } = useAppStore();

  // 视图模式
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  // 是否打开创建考试模态框
  const [isCreateExamModalOpen, setIsCreateExamModalOpen] = useState(false);
  // 当前空间
  const [currentSpace, setCurrentSpace] = useState<Space | null>(null);
  // 空间加载状态
  const [isLoadingSpace, setIsLoadingSpace] = useState(true);

  // 编辑相关状态
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editTitle, setEditTitle] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [isTitleHovered, setIsTitleHovered] = useState(false);
  const [isDescriptionHovered, setIsDescriptionHovered] = useState(false);
  const titleInputRef = useRef<HTMLInputElement>(null);
  const descriptionInputRef = useRef<HTMLTextAreaElement>(null);
  // 内容列表
  const [contentList, setContentList] = useState<ContentItem[]>([]);
  // 内容加载状态
  const [isLoadingContent, setIsLoadingContent] = useState(false);
  // 内容分页
  const [contentPagination, setContentPagination] = useState({
    page: 1,
    pageSize: 30,
    totalCount: 0,
    pageCount: 0
  });
  const { t } = useTranslation();
  const router = useRouter();
  const { isChatOpen, isMobile, closeChat } = useAppStore();

  // 防重复执行的 ref
  const initializationRef = useRef<string | null>(null);

  // 获取内容列表
  const fetchContentList = useCallback(
    async (page = 1, pageSize = 30) => {
      if (!spaceId) return;

      setIsLoadingContent(true);
      try {
        const response = await getContentPageContent({
          page,
          pageSize,
          spaceId: Number(spaceId)
        });

        if (response?.data) {
          setContentList(response.data.record || []);
          setContentPagination({
            page: response.data.page || 1,
            pageSize: response.data.pageSize || 30,
            totalCount: response.data.totalCount || 0,
            pageCount: response.data.pageCount || 0
          });
        }
      } catch (error) {
        console.error('Failed to load content list:', error);
      } finally {
        setIsLoadingContent(false);
      }
    },
    [spaceId]
  );

  // 初始化空间数据
  const initializeSpace = useCallback(async () => {
    if (!spaceId) return;

    // 防止重复执行
    if (initializationRef.current === spaceId) {
      console.log('🚫 Skipping duplicate initialization for spaceId:', spaceId);
      return;
    }

    console.log('🚀 ~ initializeSpace ~ spaceId:', spaceId);
    initializationRef.current = spaceId;

    setIsLoadingSpace(true);
    try {
      const space = await getSpaceById(spaceId);
      setCurrentSpace(space || null);

      // 获取空间数据后，立即获取内容列表
      if (space) {
        await fetchContentList();
      }
    } catch (error) {
      console.error('Failed to load space:', error);
      setCurrentSpace(null);
      // 如果出错，重置标记允许重试
      initializationRef.current = null;
    } finally {
      setIsLoadingSpace(false);
    }
  }, [spaceId, getSpaceById, fetchContentList]);

  // Initialize space data
  useEffect(() => {
    initializeSpace();
  }, [initializeSpace]);

  // 处理编辑输入框的自动聚焦
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus();
      titleInputRef.current.select();
    }
  }, [isEditingTitle]);

  useEffect(() => {
    if (isEditingDescription && descriptionInputRef.current) {
      descriptionInputRef.current.focus();
      descriptionInputRef.current.select();
    }
  }, [isEditingDescription]);

  const handleStartSpaceChat = () => {
    toggleChat();
  };

  // 编辑相关处理函数
  const handleTitleEdit = () => {
    setIsEditingTitle(true);
    setEditTitle(currentSpace?.title || '');
  };

  const handleDescriptionEdit = () => {
    setIsEditingDescription(true);
    setEditDescription(currentSpace?.description || '');
  };

  const handleTitleSave = async () => {
    if (!currentSpace || !editTitle.trim() || editTitle === currentSpace.title) {
      setIsEditingTitle(false);
      return;
    }

    try {
      await updateSpace(spaceId, { title: editTitle.trim() });
      setCurrentSpace({ ...currentSpace, title: editTitle.trim() });
      setIsEditingTitle(false);
      toast.success(t('spacePage.updateSpaceSuccess'));
    } catch (error) {
      console.error('Failed to update title:', error);
      setEditTitle(currentSpace.title || '');
      setIsEditingTitle(false);
    }
  };

  const handleDescriptionSave = async () => {
    if (!currentSpace || editDescription === currentSpace.description) {
      setIsEditingDescription(false);
      return;
    }

    try {
      await updateSpace(spaceId, { description: editDescription });
      setCurrentSpace({ ...currentSpace, description: editDescription });
      setIsEditingDescription(false);
      toast.success(t('spacePage.updateSpaceSuccess'));
    } catch (error) {
      console.error('Failed to update description:', error);
      setEditDescription(currentSpace.description || '');
      setIsEditingDescription(false);
    }
  };

  const handleTitleCancel = () => {
    setEditTitle(currentSpace?.title || '');
    setIsEditingTitle(false);
  };

  const handleDescriptionCancel = () => {
    setEditDescription(currentSpace?.description || '');
    setIsEditingDescription(false);
  };

  const handleTitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSave();
    } else if (e.key === 'Escape') {
      handleTitleCancel();
    }
  };

  const handleDescriptionKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleDescriptionCancel();
    }
    // Ctrl/Cmd + Enter 保存
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      handleDescriptionSave();
    }
  };

  return (
    <>
      <div className="min-h-screen bg-white dark:bg-neutral-900">
        {/* Learning Hub Section */}
        <LearningHub spaceId={Number(spaceId)} />

        {/* Space Section */}
        <div className="container mx-auto max-w-7xl py-6 md:py-8 px-2 sm:px-4 transition-all duration-300 ease-out">
          {/* Space Header */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8">
            <div className="lg:max-w-[60%]">
              {isLoadingSpace ? (
                <div className="space-y-2">
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-48"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-32"></div>
                </div>
              ) : currentSpace ? (
                <div className="group relative">
                  {/* 可编辑标题 */}
                  <div
                    className="relative pr-10 mb-1"
                    onMouseEnter={() => setIsTitleHovered(true)}
                    onMouseLeave={() => setIsTitleHovered(false)}
                  >
                    {isEditingTitle ? (
                      <div className="flex items-center gap-2 w-full max-w-md">
                        <Input
                          ref={titleInputRef}
                          value={editTitle}
                          onChange={(e) => setEditTitle(e.target.value)}
                          onKeyDown={handleTitleKeyPress}
                          className="text-xl sm:text-2xl font-bold h-auto px-2 py-1"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleTitleSave}
                          className="h-8 w-8 p-0 flex-shrink-0"
                        >
                          <Check className="w-4 h-4 text-green-600" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleTitleCancel}
                          className="h-8 w-8 p-0 flex-shrink-0"
                        >
                          <X className="w-4 h-4 text-red-600" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                          {currentSpace.title || t('spacePage.untitledSpace')}
                        </h2>
                        {isTitleHovered && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={handleTitleEdit}
                            className="absolute right-0 top-1/2 -translate-y-1/2 h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Edit3 className="w-4 h-4" />
                          </Button>
                        )}
                      </>
                    )}
                  </div>

                  {/* 可编辑描述 */}
                  <div
                    className="relative pr-8"
                    onMouseEnter={() => setIsDescriptionHovered(true)}
                    onMouseLeave={() => setIsDescriptionHovered(false)}
                  >
                    {isEditingDescription ? (
                      <div className="flex items-start gap-2 w-full max-w-md">
                        <Textarea
                          ref={descriptionInputRef}
                          value={editDescription}
                          onChange={(e) => setEditDescription(e.target.value)}
                          onKeyDown={handleDescriptionKeyPress}
                          className="min-h-[2.5rem] px-2 py-1 resize-none"
                          rows={2}
                          placeholder={t('spacePage.noDescription')}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <div className="flex flex-col gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={handleDescriptionSave}
                            className="h-6 w-6 p-0 flex-shrink-0"
                          >
                            <Check className="w-3 h-3 text-green-600" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={handleDescriptionCancel}
                            className="h-6 w-6 p-0 flex-shrink-0"
                          >
                            <X className="w-3 h-3 text-red-600" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <p className="text-gray-600 dark:text-gray-400 mt-1 min-h-[1.5rem]">
                          {currentSpace.description || t('spacePage.noDescription')}
                        </p>
                        {isDescriptionHovered && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={handleDescriptionEdit}
                            className="absolute right-0 top-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Edit3 className="w-3 h-3" />
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                    {t('spacePage.spaceNotFound')}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">{t('spacePage.spaceNotFoundDescription')}</p>
                </div>
              )}
            </div>

            <div className="flex flex-wrap items-center justify-end gap-2 sm:gap-3 lg:max-w-[40%] w-full">
              <Button
                variant="outline"
                className="flex items-center gap-2 transition-all duration-200 px-3 text-xs sm:px-4 sm:text-sm"
                onClick={handleStartSpaceChat}
              >
                <Eye className="w-4 h-4" />
                {t('spacePage.chat')}
              </Button>
              <Button
                variant="outline"
                className="flex items-center gap-2 transition-all duration-200 px-3 text-xs sm:px-4 sm:text-sm"
                onClick={() => {
                  return toast.info(t('common.functionUnderDevelopment'));
                  // router.push(`/exam/results`)
                }}
              >
                <Calendar className="w-4 h-4" />
                {t('spacePage.viewResults')}
              </Button>
              <Button
                onClick={() => {
                  return toast.info(t('common.functionUnderDevelopment'));
                  // setIsCreateExamModalOpen(true)
                }}
                className="bg-black text-white hover:bg-gray-800 flex items-center gap-2 transition-all duration-200 px-3 text-xs sm:px-4 sm:text-sm"
              >
                <FileText2 className="w-4 h-4" />
                {t('spacePage.createExam.step4')}
              </Button>
            </div>
          </div>

          {/* Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {isLoadingContent ? (
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16"></div>
                ) : (
                  `${contentPagination.totalCount} ${t('spacePage.items')}`
                )}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
                className="transition-all duration-200"
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
                className="transition-all duration-200"
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Content Grid/List */}
          <ContentGrid
            items={
              // 显示实际内容
              contentList.map((content) => ({
                id: String(content.id),
                title: content.title || t('spacePage.untitledContent'),
                // imageUrl:
                //   content.fileUrl ||
                //   `/images/placeholder.svg?height=200&width=350&text=${encodeURIComponent(content.title || 'Content')}`,
                type: 'article' as const,
                date: content.createAt
                  ? new Date(content.createAt).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })
                  : undefined,
                space: {
                  name: currentSpace?.title || 'Unknown Space',
                  id: spaceId
                }
              }))
            }
            actions={{
              onTitleEdit: (id, newTitle) => {
                console.log('Edit title:', id, newTitle);
                setContentList((prev) =>
                  prev.map((item) => (item.id === Number(id) ? { ...item, title: newTitle } : item))
                );
              },
              onMove: (id, spaceId) => console.log('Move to space:', id, spaceId),
              onDelete: (id) => {
                setContentList((prev) => prev.filter((item) => item.id !== Number(id)));
              },
              onShare: (id) => console.log('Share:', id),
              onClick: (id) => {
                router.push(`/article/${id}`);
              }
            }}
            variant={viewMode}
            columns={3}
            showActions={true}
            showMetadata={false}
            emptyState={
              !isLoadingContent &&
              contentList.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-12 h-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">{t('spacePage.noContent')}</h3>
                </div>
              )
            }
          />

          {/* 分页控件 */}
          {!isLoadingContent && contentList.length > 0 && contentPagination.pageCount > 1 && (
            <div className="flex justify-center items-center gap-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchContentList(contentPagination.page - 1)}
                disabled={contentPagination.page <= 1 || isLoadingContent}
              >
                {t('spacePage.previousPage')}
              </Button>

              <span className="text-sm text-gray-600 dark:text-gray-400 px-4">
                {`${contentPagination.page} / ${contentPagination.pageCount}`}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchContentList(contentPagination.page + 1)}
                disabled={contentPagination.page >= contentPagination.pageCount || isLoadingContent}
              >
                {t('spacePage.nextPage')}
              </Button>
            </div>
          )}

          {/* Create Exam Modal */}
          <CreateExamModal isOpen={isCreateExamModalOpen} onClose={() => setIsCreateExamModalOpen(false)} />

          {/* 全局聊天窗口 */}
          <SpaceChatWindow isOpen={isChatOpen} onClose={closeChat} isMobile={isMobile} spaceId={Number(spaceId)} />
        </div>
      </div>
    </>
  );
}
