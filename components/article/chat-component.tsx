'use client';
import { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessageItem } from '@/components/chat/chat-message-item';
import { ChatInputArea } from '@/components/chat/chat-input-area';
import { ChatLoadingIndicator } from '@/components/chat/chat-loading-indicator';
import { CollectNoteModal } from '@/components/notes/collect-note-modal';
import { MessageCircle, HelpCircle, Brain, MicIcon, Layers, Search, Calendar, Plus } from 'lucide-react';
import { postLlmChatContentId } from '@/servers/api/wendangneirongliaotian';
import { postLlmChatContentIdStream } from '@/lib/api/streaming';
import { getContentNewSessionId } from '@/servers/api/wendangneirong';
import { useAppDataStore, type Model } from '@/lib/stores/app-data-store';
import type { ChatMessage } from '@/types';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { saveChatToStorage, loadChatFromStorage, clearChatFromStorage } from '@/lib/utils/chat-storage';
import { useAppStore } from '@/lib/stores';
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/api/hooks';

interface ChatComponentProps {
  contentId: number;
  initialInputValue?: string;
  initialSelectedModelId?: string;
  autoSend?: boolean;
  autoSendType?: 'video' | 'audio' | 'doc'; // 新增：自动发送类型
  onAutoSendComplete?: () => void;
  contentData?: any; // 添加 contentData 用于判断内容类型
}

export function ChatComponent({
  contentId,
  initialInputValue,
  initialSelectedModelId,
  autoSend = false,
  autoSendType,
  onAutoSendComplete,
  contentData
}: ChatComponentProps) {
  const { t } = useTranslation();
  const { models } = useAppDataStore();
  const queryClient = useQueryClient();

  // 基础状态
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // UI 状态
  const [isCollectModalOpen, setIsCollectModalOpen] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const [showGlobe, setShowGlobe] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Array<{ id: number; name: string; size: number }>>([]);

  // 防重复执行的引用（不会触发重新渲染）
  const hasAutoSentRef = useRef(false);

  const { openUpgradeModal } = useAppStore();
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 从本地存储加载聊天记录
  useEffect(() => {
    if (contentId) {
      const storedMessages = loadChatFromStorage('content', contentId);
      setMessages(storedMessages);
    }
  }, [contentId]);

  // 保存聊天记录到本地存储
  useEffect(() => {
    if (contentId && messages.length > 0) {
      saveChatToStorage('content', contentId, messages);
    }
  }, [contentId, messages]);

  // 创建新聊天 - 清空聊天记录和本地存储
  const handleNewChat = useCallback(async () => {
    try {
      // 先调用新建会话接口
      await getContentNewSessionId({ id: contentId });

      // 清空聊天记录和本地存储
      setMessages([]);
      setInputValue('');
      if (contentId) {
        clearChatFromStorage('content', contentId);
      }
    } catch (error: any) {
      console.error('New session creation failed:', error);
      toast.error(error?.msg || error?.message || t('common.error'));
    }
  }, [contentId, t]);

  // 移除了初始化逻辑，现在由 HybridAuthProvider 统一管理

  // 简化的模型设置逻辑
  useEffect(() => {
    if (models.length > 0 && !selectedModel) {
      if (initialSelectedModelId) {
        const foundModel = models.find(
          (m) => m.id?.toString() === initialSelectedModelId || m.name === initialSelectedModelId
        );
        setSelectedModel(foundModel || models[0]);
      } else {
        setSelectedModel(models[0]);
      }
    }
  }, [models, selectedModel, initialSelectedModelId]);

  // 统一的 endpoint 获取函数
  const getEndpointForType = useCallback((type?: 'video' | 'audio' | 'doc'): string | undefined => {
    switch (type) {
      case 'video':
        return '/llm/video/describe';
      case 'audio':
        return '/llm/audio/describe';
      case 'doc':
        return '/llm/doc/describe'; // 添加 doc 类型的 endpoint
      default:
        return undefined; // 使用默认 endpoint
    }
  }, []);

  // 发送消息的核心逻辑 - 支持传入参数或使用当前状态
  const handleSendMessage = useCallback(
    async (messageText?: string, model?: Model) => {
      const textToSend = messageText || inputValue.trim();
      const modelToUse = model || selectedModel;
      const isAutoSend = !!messageText || (messageText === '' && autoSendType); // 修正自动发送检测逻辑

      // 统一处理所有类型的 endpoint
      let endpoint: string | undefined;
      if (isAutoSend) {
        // 优先使用传入的 autoSendType，然后使用 contentData 中的类型
        const typeToUse = autoSendType || contentData?.type;
        const shouldUseSpecialEndpoint = typeToUse && !contentData?.fileUrl;

        if (shouldUseSpecialEndpoint) {
          endpoint = getEndpointForType(typeToUse);
          console.log(`🎯 Using special endpoint for ${typeToUse}: ${endpoint}`);
        }
      }

      const newMessage: ChatMessage = {
        id: String(Date.now()),
        sender: 'user',
        text: textToSend,
        timestamp: new Date()
      };

      if (!textToSend && !isAutoSend) {
        return;
      }

      if (!modelToUse) {
        toast.error(t('chat.selectModel'));
        return;
      }

      if (isLoading) {
        return;
      }

      setIsLoading(true);
      setMessages((prev) => [...prev, newMessage]);
      setInputValue('');

      try {
        const assistantMessage: ChatMessage = {
          id: String(Date.now() + 1),
          sender: 'ai',
          text: '',
          timestamp: new Date()
        };

        setMessages((prev) => [...prev, assistantMessage]);

        await postLlmChatContentIdStream(
          {
            contentId,
            question: textToSend,
            modelId: modelToUse.id,
            uploadId: uploadedFiles.map((file) => file.id)
          },
          // onMessage
          (chunk) => {
            setMessages((prev) => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.sender === 'ai') {
                lastMessage.text += chunk;
              }
              return newMessages;
            });
          },
          // onError
          (error: any) => {
            console.error('Streaming error:', error);
            setIsLoading(false);
            toast.error(error.message || t('chat.sendFailed'));
          },
          // onComplete
          () => {
            // 确保加载状态被正确清除（防止没有数据的情况）
            setIsLoading(false);

            // 如果是 video 或 audio 类型的自动发送，完成后刷新 contentData
            if (isAutoSend && endpoint && (autoSendType === 'video' || autoSendType === 'audio')) {
              console.log(`🔄 Refreshing contentData for ${autoSendType} type after auto-send completion`);
              queryClient.invalidateQueries({
                queryKey: queryKeys.contentDetail(contentId)
              });
            }
          },
          // options
          undefined,
          // completionConfig
          undefined,
          // endpoint
          endpoint
        );

        // 保存聊天记录
        saveChatToStorage('content', contentId, [...messages, newMessage, assistantMessage]);
      } catch (error: any) {
        console.error('Send message error:', error);
        setIsLoading(false);

        if (error?.response?.status === 402) {
          openUpgradeModal();
        } else {
          toast.error(error.message || t('chat.sendFailed'));
        }
      }
    },
    [
      inputValue,
      selectedModel,
      contentId,
      isLoading,
      uploadedFiles,
      autoSendType,
      contentData,
      getEndpointForType,
      queryClient,
      t,
      openUpgradeModal,
      messages
    ]
  );

  // 重置自动发送状态当props改变时
  useEffect(() => {
    hasAutoSentRef.current = false;
  }, [autoSend, autoSendType, contentId]);

  // 简化的自动发送逻辑 - 统一处理所有初始化和自动发送
  useEffect(() => {
    // 检查是否需要自动发送
    // 对于特殊类型（video/audio/doc），即使没有 initialInputValue 也可以自动发送
    const hasSpecialType = autoSendType && ['video', 'audio', 'doc'].includes(autoSendType);
    const shouldAutoSend = autoSend && selectedModel && !isLoading && messages.length === 0 &&
                          (initialInputValue || hasSpecialType) && !hasAutoSentRef.current; // 添加防重复检查

    console.log('🚀 ~ useEffect ~ shouldAutoSend:', shouldAutoSend);
    console.log('🚀 ~ useEffect ~ autoSend:', autoSend);
    console.log('🚀 ~ useEffect ~ autoSendType:', autoSendType);
    console.log('🚀 ~ useEffect ~ hasSpecialType:', hasSpecialType);
    console.log('🚀 ~ useEffect ~ initialInputValue:', initialInputValue);
    console.log('🚀 ~ useEffect ~ selectedModel:', selectedModel?.name);
    console.log('🚀 ~ useEffect ~ hasAutoSent:', hasAutoSentRef.current);

    if (shouldAutoSend) {
      // 立即标记为已执行，防止重复
      hasAutoSentRef.current = true;

      // 直接发送，不设置到输入框
      const timer = setTimeout(async () => {
        try {
          // 对于特殊类型，使用默认消息或空字符串
          const messageToSend = initialInputValue || (hasSpecialType ? '' : '');
          console.log('🎯 Auto-sending message:', messageToSend, 'with type:', autoSendType);
          await handleSendMessage(messageToSend, selectedModel);
          onAutoSendComplete?.();
        } catch (error) {
          console.error('Auto-send failed:', error);
          onAutoSendComplete?.();
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [autoSend, autoSendType, initialInputValue, selectedModel, isLoading, messages.length, onAutoSendComplete, handleSendMessage]); // 移除 hasAutoSent 从依赖数组

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      setTimeout(() => {
        const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement;
        if (scrollElement) {
          scrollElement.scrollTo({
            top: scrollElement.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  }, []);

  // 当消息更新时自动滚动
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const copyMessage = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t('chat.copySuccess'));
  };

  const handleCollectNote = (text: string) => {
    setSelectedText(text);
    setIsCollectModalOpen(true);
  };

  // Quick action buttons data
  const quickActions = [
    {
      icon: HelpCircle,
      label: t('article.chat.quickActions.quiz'),
      prompt: t('article.chat.quickActions.quizPrompt'),
      color: 'bg-blue-50 hover:bg-blue-100 text-blue-700'
    },
    {
      icon: Brain,
      label: t('article.chat.quickActions.mindMap'),
      prompt: t('article.chat.quickActions.mindMapPrompt'),
      color: 'bg-purple-50 hover:bg-purple-100 text-purple-700'
    },
    {
      icon: Layers,
      label: t('article.chat.quickActions.flashcards'),
      prompt: t('article.chat.quickActions.flashcardsPrompt'),
      color: 'bg-orange-50 hover:bg-orange-100 text-orange-700'
    },
    {
      icon: Calendar,
      label: t('article.chat.quickActions.schedule'),
      prompt: t('article.chat.quickActions.schedulePrompt'),
      color: 'bg-indigo-50 hover:bg-indigo-100 text-indigo-700'
    }
  ];

  const handleQuickActionClick = (prompt: string) => {
    setInputValue(prompt);
    setTimeout(() => {
      const inputElement = document.querySelector(
        'textarea[placeholder*="' + t('article.chat.inputPlaceholder') + '"]'
      ) as HTMLTextAreaElement;
      if (inputElement) {
        inputElement.focus();
      }
    }, 100);
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-neutral-900">
      {/* New Chat Button - 只在有聊天记录时显示 */}
      {messages.length > 0 && (
        <div className="flex-shrink-0 p-3 border-b dark:border-neutral-700">
          <Button
            onClick={handleNewChat}
            variant="outline"
            size="sm"
            className="w-full h-8 text-sm font-medium gap-2 hover:bg-gray-50 dark:hover:bg-neutral-800"
          >
            <Plus className="w-4 h-4" />
            {t('article.chat.newChat')}
          </Button>
        </div>
      )}

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4 overflow-hidden" ref={scrollAreaRef}>
        {messages.length === 0 ? (
          // Empty State
          <div className="flex flex-col items-center justify-center h-full py-8">
            <div className="mb-6">
              <div className="w-20 h-20 bg-gray-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
                <MessageCircle className="w-10 h-10 text-gray-400" strokeWidth={1.5} />
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-8 text-center">
              {t('article.chat.title')}
            </h3>

            <div className="grid grid-cols-2 gap-3 w-full max-w-xs">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  onClick={() => handleQuickActionClick(action.prompt)}
                  className={`h-12 flex items-center justify-center gap-2 text-sm font-medium border-gray-200 dark:border-neutral-700 ${action.color} dark:bg-neutral-800 dark:hover:bg-neutral-700 dark:text-gray-300`}
                >
                  <action.icon className="w-4 h-4" />
                  {action.label}
                </Button>
              ))}
            </div>
          </div>
        ) : (
          // Messages Display
          <div className="space-y-3 md:space-y-4">
            {messages.map((msg, index) => (
              <ChatMessageItem
                key={msg.id}
                message={msg}
                onCopyMessage={copyMessage}
                handleCollectNote={handleCollectNote}
                isLoading={isLoading && msg.sender === 'ai' && index === messages.length - 1}
                index={index}
              />
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Input Area */}
      <ChatInputArea
        inputValue={inputValue}
        showGlobe={showGlobe}
        setShowGlobe={setShowGlobe}
        placeholder={t('article.chat.inputPlaceholder')}
        onInputValueChange={setInputValue}
        onSendMessage={() => handleSendMessage()}
        selectedMode={selectedModel?.name || 'Default'}
        onModeChange={(modelName: string) => {
          const foundModel = models.find((m) => m.name === modelName);
          if (foundModel) {
            setSelectedModel(foundModel);
          }
        }}
        isLoading={isLoading}
        availableModels={models}
        uploadedFiles={uploadedFiles}
        onUploadedFilesChange={setUploadedFiles}
      />

      <CollectNoteModal
        isOpen={isCollectModalOpen}
        onClose={() => setIsCollectModalOpen(false)}
        selectedText={selectedText}
        contentId={contentId}
        mode="create-with-content"
      />
    </div>
  );
}
