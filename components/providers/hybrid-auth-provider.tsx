'use client';

import { useEffect, useState } from 'react';
import { useHybridAuthStore } from '@/lib/stores/hybrid-auth-store';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { TokenManager } from '@/lib/api/client';

interface HybridAuthProviderProps {
  children: React.ReactNode;
}

export function HybridAuthProvider({ children }: HybridAuthProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false);
  const { syncFromCookies, syncToCookies, clearAuth, refreshUser, setAuthenticated, isAuthenticated } =
    useHybridAuthStore();

  const { initializeAppData } = useAppDataStore();

  useEffect(() => {
    // 标记为已水合
    setIsHydrated(true);

    // 初始化混合认证状态
    const initializeHybridAuth = async () => {
      try {
        // 1. 从cookie同步状态到localStorage
        syncFromCookies();

        // 2. 检查token有效性
        const token = TokenManager.getToken();
        // 获取当前认证状态（避免在依赖数组中使用）
        const currentAuthState = useHybridAuthStore.getState().isAuthenticated;

        if (token) {
          // 检查token是否即将过期
          if (TokenManager.isTokenExpiring()) {
            console.warn('Token is expiring, clearing auth state');
            TokenManager.clearTokens();
            clearAuth();
            return;
          }

          let needsAppDataInit = false;

          // 如果有token但认证状态为false，尝试验证token
          if (!currentAuthState) {
            try {
              console.log('🔐 找到有效令牌但未经过身份验证，正在刷新用户...');
              await refreshUser();
              setAuthenticated(true);
              needsAppDataInit = true;

              // 确保cookie状态同步
              syncToCookies();
            } catch (error) {
              console.warn('令牌验证失败：', error);
              TokenManager.clearTokens();
              clearAuth();
              return;
            }
          } else {
            // 用户已认证，也需要初始化应用数据
            needsAppDataInit = true;
          }

          // 统一初始化应用数据
          if (needsAppDataInit) {
            console.log('🔐 用户已验证，正在初始化应用数据……');
            await refreshUser();
            await initializeAppData();
          }
        } else {
          // 没有token，确保清除认证状态和初始化状态
          clearAuth();
        }

        // 3. 确保状态同步到cookie
        syncToCookies();
      } catch (error) {
        console.error('Hybrid auth initialization error:', error);
        clearAuth();
      }
    };

    initializeHybridAuth();
  }, [syncFromCookies, syncToCookies, clearAuth, refreshUser, setAuthenticated, initializeAppData]);

  // 监听存储变化（跨标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'hybrid-auth-storage' && e.newValue) {
        try {
          const newState = JSON.parse(e.newValue);
          // 如果其他标签页更新了认证状态，同步到cookie
          setTimeout(() => {
            syncToCookies();
          }, 100);
        } catch (error) {
          console.error('Failed to sync storage change:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [syncToCookies]);

  // 页面可见性变化时同步状态
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面重新可见时，检查cookie和localStorage的一致性
        syncFromCookies();

        // 检查token是否还有效
        const token = TokenManager.getToken();
        if (token && TokenManager.isTokenExpiring()) {
          clearAuth();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [syncFromCookies, clearAuth]);

  // 防止SSR水合不匹配
  if (!isHydrated) {
    return <div style={{ opacity: 0, pointerEvents: 'none' }}>{children}</div>;
  }

  return <>{children}</>;
}

// 混合认证守护组件
export function HybridAuthGuard({
  children,
  fallback,
  requireAuth = true
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}) {
  const isAuthenticated = useHybridAuthStore((state) => state.isAuthenticated);
  const isLoading = useHybridAuthStore((state) => state.isLoading);
  const user = useHybridAuthStore((state) => state.user);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // 如果需要认证但用户未登录
  if (requireAuth && !isAuthenticated) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="mb-6">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold mb-2">Authentication Required</h2>
              <p className="text-muted-foreground">
                Please log in to access this page. You'll be redirected after signing in.
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => (window.location.href = '/login')}
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90 py-2 px-4 rounded-md transition-colors"
              >
                Go to Login
              </button>
              <button
                onClick={() => window.history.back()}
                className="w-full border border-border hover:bg-muted py-2 px-4 rounded-md transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      )
    );
  }

  // 如果不需要认证或用户已登录
  return <>{children}</>;
}

// 用户信息显示组件示例
export function UserDisplay() {
  const user = useHybridAuthStore((state) => state.user);
  const isAuthenticated = useHybridAuthStore((state) => state.isAuthenticated);
  const preferredLocale = useHybridAuthStore((state) => state.preferredLocale);

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
        {user.avatar ? (
          <img src={user.avatar} alt={user.username} className="w-10 h-10 rounded-full" />
        ) : (
          <span className="text-primary font-medium">{user.username.charAt(0).toUpperCase()}</span>
        )}
      </div>

      <div className="flex-1 min-w-0">
        <p className="font-medium truncate">{user.nickname || user.username}</p>
        <p className="text-sm text-muted-foreground truncate">
          {user.email} • {preferredLocale.toUpperCase()}
        </p>
      </div>
    </div>
  );
}
