"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Link, FileText, X, Loader2 } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { useAppDataStore } from "@/lib/stores/app-data-store"
import { useArticleCacheStore } from "@/lib/stores/article-cache-store"
import { postContentPasteCreatePaste } from "@/servers/api/wenben"

interface PasteContentModalProps {
  isOpen: boolean
  onClose: () => void
  spaceId?: number
  onCreateSpace?: (title: string, description?: string) => Promise<number | null>
}

export function PasteContentModal({ isOpen, onClose, spaceId, onCreateSpace }: PasteContentModalProps) {
  const { t } = useTranslation()
  const [urlInput, setUrlInput] = useState("")
  const [textInput, setTextInput] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStep, setProcessingStep] = useState("")
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const router = useRouter()
  const { createContent } = useAppDataStore()
  const { setNewArticleChat } = useArticleCacheStore()

  // Auto-focus textarea when modal opens
  useEffect(() => {
    if (isOpen && textareaRef.current) {
      setTimeout(() => {
        textareaRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  // 粘贴事件
  const handlePaste = async (e: React.ClipboardEvent) => {
    e.preventDefault() // 阻止默认粘贴行为
    const pastedText = e.clipboardData.getData("text")

    // 检查粘贴内容是否像URL
    const urlPattern = /^https?:\/\/.+/i
    if (urlPattern.test(pastedText.trim())) {
      setUrlInput(pastedText.trim())
      setTextInput("")
    } else {
      setTextInput(pastedText)
      setUrlInput("")
    }
  }

  // 确保有 spaceId，如果没有则创建新空间
  const ensureSpaceId = async (title: string, description?: string): Promise<number> => {
    let currentSpaceId = spaceId

    // 如果仍然没有 spaceId，创建新空间
    if (!currentSpaceId) {
      // 优先使用传入的 onCreateSpace 方法
      if (onCreateSpace) {
        const newSpaceId = await onCreateSpace(title, description || title)
        if (!newSpaceId) {
          throw new Error('Failed to create space')
        }
        currentSpaceId = newSpaceId
      } else {
        throw new Error('No space ID and no create space function provided')
      }
    }

    return currentSpaceId
  }

  // Handle content creation
  const handleAddContent = async () => {
    if (!urlInput.trim() && !textInput.trim()) {
      toast.error(t("pasteModal.error"))
      return
    }

    setIsProcessing(true)

    try {
      const isUrl = urlInput.trim()
      const isText = textInput.trim()
      const content = isUrl || isText
      const title = content.length > 50 ? content.slice(0, 50) + '...' : content
      
      setProcessingStep(t("pasteModal.processing"))
      
      // 确保有 spaceId
      const currentSpaceId = await ensureSpaceId(
        title,
        isUrl ? `URL: ${urlInput}` : `Text: ${textInput.slice(0, 100)}...`
      )

      // 第一步：调用粘贴接口获取 uploadId
      const pasteResponse = await postContentPasteCreatePaste({
        url: isUrl ? urlInput.trim() : undefined,
        text: isText ? textInput.trim() : undefined
      })

      if (pasteResponse?.code !== '0' || !pasteResponse?.data?.id) {
        throw new Error(pasteResponse?.msg || 'Failed to create paste content')
      }

      const uploadId = pasteResponse.data.id

      // 第二步：使用 uploadId 创建内容
      const contentResponse = await createContent({
        spaceId: currentSpaceId,
        uploadId: uploadId,
        title: title
      })

      if (contentResponse?.code !== '0' || !contentResponse?.data?.id) {
        throw new Error(contentResponse?.msg || 'Failed to create content')
      }

      const contentId = contentResponse.data.id

      // 如果是文本内容，记录到 article cache 中以便后续处理
      if (isText) {
        setNewArticleChat(contentId, {
          modelId: 'default',
          aiQuery: textInput
        })
      }

      // 跳转到文章内容页
      router.push(`/article/${contentId}`)
      
      toast.success(t("pasteModal.success"))
      handleClose()
    } catch (error) {
      console.error('Paste content error:', error)
      toast.error(error instanceof Error ? error.message : t("pasteModal.error"))
    } finally {
      setIsProcessing(false)
      setProcessingStep("")
    }
  }

  const handleClose = () => {
    if (!isProcessing) {
      setUrlInput("")
      setTextInput("")
      setProcessingStep("")
      onClose()
    }
  }

  const isValidContent = urlInput.trim() || textInput.trim()

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[90%] sm:max-w-2xl p-0 gap-0 bg-white dark:bg-gray-900">
        {/* Header */}
        <DialogHeader className="p-6 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link className="h-5 w-5 text-muted-foreground" />
              <DialogTitle className="text-lg font-medium">{t("pasteModal.title")}</DialogTitle>
            </div>
            {/* <Button variant="ghost" size="icon" onClick={handleClose} disabled={isProcessing} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button> */}
          </div>
          <p className="text-sm text-muted-foreground mt-2">{t("pasteModal.subtitle")}</p>
        </DialogHeader>

        {/* Content */}
        <div className="px-6 pb-6 space-y-6">
          {/* URL Input Section */}
          <div className="space-y-3">
            <Input
              placeholder={t("pasteModal.urlPlaceholder")}
              value={urlInput}
              onChange={(e) => {
                setUrlInput(e.target.value)
                if (e.target.value.trim()) {
                  setTextInput("")
                }
              }}
              onPaste={handlePaste}
              disabled={isProcessing}
              className="h-12 text-base"
            />
          </div>

          {/* Divider */}
          <div className="flex items-center gap-4">
            <Separator className="flex-1" />
            <span className="text-sm text-muted-foreground px-2">{t("pasteModal.or")}</span>
            <Separator className="flex-1" />
          </div>

          {/* Text Input Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <h3 className="font-medium">{t("pasteModal.pasteTextTitle")}</h3>
            </div>
            <p className="text-sm text-muted-foreground">{t("pasteModal.pasteTextDesc")}</p>
            <Textarea
              ref={textareaRef}
              placeholder={t("pasteModal.textPlaceholder")}
              value={textInput}
              onChange={(e) => {
                setTextInput(e.target.value)
                if (e.target.value.trim()) {
                  setUrlInput("")
                }
              }}
              onPaste={handlePaste}
              disabled={isProcessing}
              className="min-h-[200px] resize-none text-base"
            />
          </div>

          {/* Processing Status */}
          {isProcessing && (
            <div className="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">{t("pasteModal.processing")}</p>
                {processingStep && <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">{processingStep}</p>}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={handleClose} disabled={isProcessing} className="px-6">
              {t("pasteModal.cancel")}
            </Button>
            <Button
              onClick={handleAddContent}
              disabled={!isValidContent || isProcessing}
              className="px-6 bg-black hover:bg-gray-800 text-white"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t("pasteModal.processing")}
                </>
              ) : (
                t("pasteModal.add")
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
